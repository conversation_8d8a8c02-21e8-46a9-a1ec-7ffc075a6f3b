import {
  Page<PERSON>ontainer,
  ProCard,
  ProTable,
  ProForm,
  ProFormText,
  ProFormSelect,
  ProFormDateRangePicker,
} from '@ant-design/pro-components';
import { Button, message, Modal, Space, Tabs, Tag, Progress, Upload, Descriptions } from 'antd';
import { 
  DownloadOutlined, 
  EyeOutlined, 
  CloudDownloadOutlined,
  SyncOutlined,
  FileTextOutlined,
  InboxOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import React, { useState, useRef } from 'react';
import type { ActionType, ProColumns } from '@ant-design/pro-components';

const { Dragger } = Upload;

// 定义数据类型
interface ArchivedInvoice {
  id: string;
  invoiceNo: string;
  invoiceType: string;
  patientName: string;
  amount: number;
  issueDate: string;
  archiveDate: string;
  fileFormat: string;
  fileSize: number;
  downloadCount: number;
  status: string;
  filePath: string;
  checksum: string;
}

interface ArchiveTask {
  id: string;
  taskNo: string;
  taskType: string;
  startDate: string;
  endDate: string;
  totalCount: number;
  successCount: number;
  failCount: number;
  status: string;
  createTime: string;
  completeTime?: string;
  operator: string;
}

const InvoiceArchive: React.FC = () => {
  const [activeTab, setActiveTab] = useState('archived');
  const [modalVisible, setModalVisible] = useState(false);
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState<any>(null);
  const actionRef = useRef<ActionType>();

  // 模拟数据
  const mockArchivedData: ArchivedInvoice[] = [
    {
      id: '1',
      invoiceNo: 'PJ20250110001',
      invoiceType: '门诊医疗电子票据',
      patientName: '张三',
      amount: 125.50,
      issueDate: '2025-01-10 09:30:00',
      archiveDate: '2025-01-10 10:00:00',
      fileFormat: 'PDF',
      fileSize: 245760, // bytes
      downloadCount: 3,
      status: '已存档',
      filePath: '/archive/2025/01/PJ20250110001.pdf',
      checksum: 'a1b2c3d4e5f6',
    },
    {
      id: '2',
      invoiceNo: 'PJ20250110002',
      invoiceType: '住院医疗电子票据',
      patientName: '李四',
      amount: 1250.00,
      issueDate: '2025-01-10 11:00:00',
      archiveDate: '2025-01-10 11:30:00',
      fileFormat: 'XML',
      fileSize: 156432,
      downloadCount: 1,
      status: '已存档',
      filePath: '/archive/2025/01/PJ20250110002.xml',
      checksum: 'f6e5d4c3b2a1',
    },
  ];

  const mockArchiveTaskData: ArchiveTask[] = [
    {
      id: '1',
      taskNo: 'TASK20250110001',
      taskType: '定时同步',
      startDate: '2025-01-10 00:00:00',
      endDate: '2025-01-10 23:59:59',
      totalCount: 150,
      successCount: 148,
      failCount: 2,
      status: '已完成',
      createTime: '2025-01-10 08:00:00',
      completeTime: '2025-01-10 08:30:00',
      operator: '系统自动',
    },
    {
      id: '2',
      taskNo: 'TASK20250110002',
      taskType: '手动同步',
      startDate: '2025-01-09 00:00:00',
      endDate: '2025-01-09 23:59:59',
      totalCount: 200,
      successCount: 180,
      failCount: 0,
      status: '进行中',
      createTime: '2025-01-10 14:00:00',
      operator: '张三',
    },
  ];

  // 已存档票据列配置
  const archivedColumns: ProColumns<ArchivedInvoice>[] = [
    {
      title: '票据号码',
      dataIndex: 'invoiceNo',
      key: 'invoiceNo',
      width: 140,
    },
    {
      title: '票据类型',
      dataIndex: 'invoiceType',
      key: 'invoiceType',
      width: 150,
    },
    {
      title: '患者姓名',
      dataIndex: 'patientName',
      key: 'patientName',
      width: 100,
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      width: 100,
      valueType: 'money',
    },
    {
      title: '开具时间',
      dataIndex: 'issueDate',
      key: 'issueDate',
      width: 150,
      valueType: 'dateTime',
    },
    {
      title: '存档时间',
      dataIndex: 'archiveDate',
      key: 'archiveDate',
      width: 150,
      valueType: 'dateTime',
    },
    {
      title: '文件格式',
      dataIndex: 'fileFormat',
      key: 'fileFormat',
      width: 100,
      render: (_, record) => {
        const formatConfig = {
          'PDF': { color: 'red', text: 'PDF' },
          'XML': { color: 'blue', text: 'XML' },
          'JSON': { color: 'green', text: 'JSON' },
        };
        const config = formatConfig[record.fileFormat as keyof typeof formatConfig];
        return <Tag color={config?.color}>{config?.text}</Tag>;
      },
    },
    {
      title: '文件大小',
      dataIndex: 'fileSize',
      key: 'fileSize',
      width: 100,
      render: (_, record) => {
        const size = record.fileSize;
        if (size < 1024) return `${size}B`;
        if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`;
        return `${(size / (1024 * 1024)).toFixed(1)}MB`;
      },
    },
    {
      title: '下载次数',
      dataIndex: 'downloadCount',
      key: 'downloadCount',
      width: 100,
      valueType: 'digit',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (_, record) => {
        const statusConfig = {
          '已存档': { color: 'green', text: '已存档' },
          '存档中': { color: 'blue', text: '存档中' },
          '存档失败': { color: 'red', text: '存档失败' },
        };
        const config = statusConfig[record.status as keyof typeof statusConfig];
        return <Tag color={config?.color}>{config?.text}</Tag>;
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handlePreview(record)}
          >
            预览
          </Button>
          <Button
            type="link"
            size="small"
            icon={<DownloadOutlined />}
            onClick={() => handleDownload(record.id)}
          >
            下载
          </Button>
          <Button
            type="link"
            size="small"
            icon={<SyncOutlined />}
            onClick={() => handleResync(record.id)}
          >
            重新同步
          </Button>
        </Space>
      ),
    },
  ];

  // 存档任务列配置
  const archiveTaskColumns: ProColumns<ArchiveTask>[] = [
    {
      title: '任务编号',
      dataIndex: 'taskNo',
      key: 'taskNo',
      width: 140,
    },
    {
      title: '任务类型',
      dataIndex: 'taskType',
      key: 'taskType',
      width: 100,
      valueEnum: {
        '定时同步': { text: '定时同步', status: 'Processing' },
        '手动同步': { text: '手动同步', status: 'Success' },
        '批量导入': { text: '批量导入', status: 'Warning' },
      },
    },
    {
      title: '时间范围',
      key: 'dateRange',
      width: 200,
      render: (_, record) => `${record.startDate} ~ ${record.endDate}`,
    },
    {
      title: '总数量',
      dataIndex: 'totalCount',
      key: 'totalCount',
      width: 100,
      valueType: 'digit',
    },
    {
      title: '成功数量',
      dataIndex: 'successCount',
      key: 'successCount',
      width: 100,
      valueType: 'digit',
    },
    {
      title: '失败数量',
      dataIndex: 'failCount',
      key: 'failCount',
      width: 100,
      valueType: 'digit',
      render: (_, record) => (
        <span style={{ color: record.failCount > 0 ? '#ff4d4f' : '#52c41a' }}>
          {record.failCount}
        </span>
      ),
    },
    {
      title: '进度',
      key: 'progress',
      width: 120,
      render: (_, record) => {
        const processed = record.successCount + record.failCount;
        const percent = record.totalCount > 0 ? (processed / record.totalCount) * 100 : 0;
        return (
          <Progress
            percent={Number(percent.toFixed(1))}
            size="small"
            status={record.status === '已完成' ? 'success' : 'active'}
          />
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (_, record) => {
        const statusConfig = {
          '进行中': { color: 'blue', text: '进行中' },
          '已完成': { color: 'green', text: '已完成' },
          '已失败': { color: 'red', text: '已失败' },
          '已取消': { color: 'gray', text: '已取消' },
        };
        const config = statusConfig[record.status as keyof typeof statusConfig];
        return <Tag color={config?.color}>{config?.text}</Tag>;
      },
    },
    {
      title: '操作员',
      dataIndex: 'operator',
      key: 'operator',
      width: 100,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 150,
      valueType: 'dateTime',
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewTaskDetail(record)}
          >
            详情
          </Button>
          {record.status === '进行中' && (
            <Button
              type="link"
              size="small"
              danger
              onClick={() => handleCancelTask(record.id)}
            >
              取消
            </Button>
          )}
        </Space>
      ),
    },
  ];

  const handlePreview = (record: ArchivedInvoice) => {
    setEditingRecord(record);
    setPreviewModalVisible(true);
  };

  const handleDownload = (id: string) => {
    message.success('文件下载已开始');
  };

  const handleResync = (id: string) => {
    Modal.confirm({
      title: '确认重新同步',
      content: '确定要重新同步这个票据吗？',
      onOk: () => {
        message.success('重新同步任务已启动');
        actionRef.current?.reload();
      },
    });
  };

  const handleViewTaskDetail = (record: ArchiveTask) => {
    setEditingRecord(record);
    setModalVisible(true);
  };

  const handleCancelTask = (id: string) => {
    Modal.confirm({
      title: '确认取消任务',
      content: '确定要取消这个存档任务吗？',
      onOk: () => {
        message.success('任务已取消');
        actionRef.current?.reload();
      },
    });
  };

  const handleCreateSyncTask = () => {
    Modal.confirm({
      title: '创建同步任务',
      content: '确定要创建新的票据同步任务吗？',
      onOk: () => {
        message.success('同步任务已创建');
        actionRef.current?.reload();
      },
    });
  };

  const handleBatchUpload = (info: any) => {
    const { status } = info.file;
    if (status === 'done') {
      message.success(`${info.file.name} 文件上传成功`);
    } else if (status === 'error') {
      message.error(`${info.file.name} 文件上传失败`);
    }
  };

  const renderPreviewModal = () => {
    if (!editingRecord) return null;

    return (
      <Modal
        title="票据预览"
        open={previewModalVisible}
        onCancel={() => setPreviewModalVisible(false)}
        footer={[
          <Button key="download" type="primary" icon={<DownloadOutlined />}>
            下载原文件
          </Button>,
          <Button key="close" onClick={() => setPreviewModalVisible(false)}>
            关闭
          </Button>,
        ]}
        width={800}
      >
        <Descriptions title="文件信息" bordered column={2}>
          <Descriptions.Item label="票据号码">{editingRecord.invoiceNo}</Descriptions.Item>
          <Descriptions.Item label="文件格式">{editingRecord.fileFormat}</Descriptions.Item>
          <Descriptions.Item label="文件大小">
            {editingRecord.fileSize < 1024 * 1024 
              ? `${(editingRecord.fileSize / 1024).toFixed(1)}KB`
              : `${(editingRecord.fileSize / (1024 * 1024)).toFixed(1)}MB`
            }
          </Descriptions.Item>
          <Descriptions.Item label="校验码">{editingRecord.checksum}</Descriptions.Item>
          <Descriptions.Item label="存档路径" span={2}>{editingRecord.filePath}</Descriptions.Item>
        </Descriptions>
        
        <div style={{ marginTop: 24, textAlign: 'center', padding: '40px 0', backgroundColor: '#fafafa' }}>
          <FileTextOutlined style={{ fontSize: '48px', color: '#1890ff' }} />
          <div style={{ marginTop: 16 }}>
            <p>票据文件预览</p>
            <p style={{ color: '#666' }}>点击下载按钮获取完整文件</p>
          </div>
        </div>
      </Modal>
    );
  };

  const tabItems = [
    {
      key: 'archived',
      label: '已存档票据',
      children: (
        <ProTable<ArchivedInvoice>
          columns={archivedColumns}
          dataSource={mockArchivedData}
          rowKey="id"
          actionRef={actionRef}
          search={{
            labelWidth: 'auto',
          }}
          toolBarRender={() => [
            <Button
              key="sync"
              type="primary"
              icon={<CloudDownloadOutlined />}
              onClick={handleCreateSyncTask}
            >
              创建同步任务
            </Button>,
            <Button
              key="export"
              icon={<DownloadOutlined />}
            >
              批量导出
            </Button>,
          ]}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
          }}
        />
      ),
    },
    {
      key: 'tasks',
      label: '存档任务',
      children: (
        <ProTable<ArchiveTask>
          columns={archiveTaskColumns}
          dataSource={mockArchiveTaskData}
          rowKey="id"
          actionRef={actionRef}
          search={false}
          toolBarRender={() => [
            <Button
              key="create"
              type="primary"
              icon={<SyncOutlined />}
              onClick={handleCreateSyncTask}
            >
              新建同步任务
            </Button>,
          ]}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
          }}
        />
      ),
    },
    {
      key: 'upload',
      label: '批量导入',
      children: (
        <div style={{ padding: '24px' }}>
          <Dragger
            name="file"
            multiple
            action="/api/upload"
            onChange={handleBatchUpload}
            style={{ marginBottom: 24 }}
          >
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
            <p className="ant-upload-hint">
              支持单个或批量上传票据文件，支持PDF、XML、JSON格式
            </p>
          </Dragger>
          
          <div style={{ marginTop: 24 }}>
            <h4>上传说明：</h4>
            <ul>
              <li>支持的文件格式：PDF、XML、JSON</li>
              <li>单个文件大小不超过10MB</li>
              <li>批量上传最多支持100个文件</li>
              <li>文件名应包含票据号码以便系统识别</li>
            </ul>
          </div>
        </div>
      ),
    },
  ];

  return (
    <PageContainer
      title="票据存档"
      content="管理电子票据的下载、存档、查阅和交付，确保票据数据的安全性和完整性"
    >
      <ProCard>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
        />
      </ProCard>
      {renderPreviewModal()}
    </PageContainer>
  );
};

export default InvoiceArchive;
